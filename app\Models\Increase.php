<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Increase extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    protected $fillable = [
        'nom_fr',
        'nom_en',
        'nom_ar',
        'percentage',
        'date_subscription',
        'date_website'
    ];

    protected $casts = [
        'percentage' => 'decimal:2',
        'date_subscription' => 'date',
        'date_website' => 'date'
    ];

    /**
     * Get the lines that belong to this increase.
     */
    public function lines(): HasMany
    {
        return $this->hasMany(Line::class, 'increase_id');
    }
}
