<?php

namespace Database\Seeders;

use App\Models\Increase;
use App\Models\Line;
use Illuminate\Database\Seeder;

class IncreaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create increases first
        $increases = [
            [
                'nom_fr' => 'Augmentation Été 2025',
                'nom_en' => 'Summer Increase 2025',
                'nom_ar' => 'زيادة صيف 2025',
                'percentage' => 15.50,
                'date_subscription' => '2025-06-01',
                'date_website' => '2025-06-15',
            ],
            [
                'nom_fr' => 'Augmentation Carburant',
                'nom_en' => 'Fuel Increase',
                'nom_ar' => 'زيادة الوقود',
                'percentage' => 8.25,
                'date_subscription' => '2025-03-01',
                'date_website' => '2025-03-10',
            ],
            [
                'nom_fr' => 'Augmentation Inflation',
                'nom_en' => 'Inflation Increase',
                'nom_ar' => 'زيادة التضخم',
                'percentage' => 12.00,
                'date_subscription' => '2025-01-01',
                'date_website' => '2025-01-15',
            ],
            [
                'nom_fr' => 'Augmentation Maintenance',
                'nom_en' => 'Maintenance Increase',
                'nom_ar' => 'زيادة الصيانة',
                'percentage' => 5.75,
                'date_subscription' => '2025-04-01',
                'date_website' => '2025-04-05',
            ],
            [
                'nom_fr' => 'Augmentation Spéciale',
                'nom_en' => 'Special Increase',
                'nom_ar' => 'زيادة خاصة',
                'percentage' => 20.00,
                'date_subscription' => '2025-07-01',
                'date_website' => '2025-07-10',
            ],
        ];

        // Create increases
        $createdIncreases = collect();
        foreach ($increases as $increaseData) {
            $createdIncreases->push(Increase::create($increaseData));
        }

        // Get some existing lines and assign them to increases
        $lines = Line::take(10)->get();

        if ($lines->isNotEmpty() && $createdIncreases->isNotEmpty()) {
            // Assign some lines to the first increase
            $lines->take(3)->each(function ($line) use ($createdIncreases) {
                $line->update(['increase_id' => $createdIncreases->get(0)->id]);
            });

            // Assign some lines to the second increase
            $lines->skip(3)->take(2)->each(function ($line) use ($createdIncreases) {
                if ($createdIncreases->get(1)) {
                    $line->update(['increase_id' => $createdIncreases->get(1)->id]);
                }
            });

            // Assign remaining lines to other increases
            $lines->skip(5)->each(function ($line, $index) use ($createdIncreases) {
                $increaseIndex = ($index % ($createdIncreases->count() - 2)) + 2;
                if ($createdIncreases->get($increaseIndex)) {
                    $line->update(['increase_id' => $createdIncreases->get($increaseIndex)->id]);
                }
            });
        }

        $this->command->info('Increases seeded successfully.');
    }
}
