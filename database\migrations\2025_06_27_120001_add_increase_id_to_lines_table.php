<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('lines', function (Blueprint $table) {
            $table->foreignId('increase_id')->nullable()->constrained('increases')->onDelete('set null');
            $table->index('increase_id');
        });
    }

    public function down(): void
    {
        Schema::table('lines', function (Blueprint $table) {
            $table->dropForeign(['increase_id']);
            $table->dropIndex(['increase_id']);
            $table->dropColumn('increase_id');
        });
    }
};
