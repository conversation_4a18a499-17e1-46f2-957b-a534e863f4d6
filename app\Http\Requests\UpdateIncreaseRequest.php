<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateIncreaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'nom_fr' => 'sometimes|required|string|max:255',
            'nom_en' => 'nullable|string|max:255',
            'nom_ar' => 'nullable|string|max:255',
            'percentage' => 'sometimes|required|numeric|min:0|max:100',
            'date_subscription' => 'sometimes|required|date',
            'date_website' => 'sometimes|required|date'
        ];
    }

    public function messages(): array
    {
        return [
            'nom_fr.required' => 'The French name is required.',
            'percentage.required' => 'The percentage is required.',
            'percentage.min' => 'The percentage must be at least 0.',
            'percentage.max' => 'The percentage must not exceed 100.',
            'date_subscription.required' => 'The subscription date is required.',
            'date_subscription.date' => 'The subscription date must be a valid date.',
            'date_website.required' => 'The website date is required.',
            'date_website.date' => 'The website date must be a valid date.'
        ];
    }
}
