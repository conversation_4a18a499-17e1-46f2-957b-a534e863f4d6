<?php

namespace Database\Seeders;

use App\Models\Line;
use App\Models\Station;
use App\Models\LineStation;
use App\Models\TariffBase;
use App\Models\Trip;
use App\Models\TariffOption;
use App\Models\WebsiteTrip;
use Illuminate\Database\Seeder;

class TripIrregulierSeeder extends Seeder
{
    private $missedStations = [];

    public function run(): void
    {

        include_once("data/NON_REGULIER.php");

        foreach ($trajet_non_reguliers as $trajet) {

            $line = Line::where('CODE_LINE', $trajet['CODE_LIGNE'])->first();
            if (!$line) {
                echo "No line found for: {$trajet['CODE_LIGNE']}\n";
                // create a new line if it doesn't exist
                $line = Line::create([
                    'CODE_LINE' => $trajet['CODE_LIGNE'],
                    'nom_fr' => $trajet['CODE_LIGNE'],
                    'nom_en' => $trajet['CODE_LIGNE'],
                    'nom_ar' => $trajet['CODE_LIGNE'],
                    'status' => true,
                ]);
                echo "Created new line: {$line->nom_fr}\n";
            }

            $startStation = Station::where('nom_fr', $trajet['CODE_ARRET'])->first();
            if (!$startStation) {
                echo "No start station found for: {$trajet['CODE_ARRET']}\n";
                $this->addMissedStation($trajet['CODE_ARRET']);
                // create a new station if it doesn't exist with this line
                $startStation = Station::create([
                    'nom_fr' => $trajet['CODE_ARRET'],
                    'nom_en' => $trajet['CODE_ARRET'],
                    'nom_ar' => $trajet['CODE_ARRET'],
                    'id_line' => $line->id,
                    'status' => true,
                ]);
                echo "Created new station: {$startStation->nom_fr}\n";
            }
            $endStation = Station::where('nom_fr', $trajet['ARR_CODE_ARRET'])->first();
            if (!$endStation) {
                echo "No end station found for: {$trajet['ARR_CODE_ARRET']}\n";
                $this->addMissedStation($trajet['ARR_CODE_ARRET']);
                // create a new station if it doesn't exist with this line
                $endStation = Station::create([
                    'nom_fr' => $trajet['ARR_CODE_ARRET'],
                    'nom_en' => $trajet['ARR_CODE_ARRET'],
                    'nom_ar' => $trajet['ARR_CODE_ARRET'],
                    'id_line' => $line->id,

                    'status' => true,
                ]);
                echo "Created new station: {$endStation->nom_fr}\n";
            }

            $tariffBases = TariffBase::where('id_subs_type', 3)
            ->whereHas('tariffs', function ($query) use ($trajet) {
                return $query->where('tariff', '=',  $trajet['TARIF_NON_REGULIER'] / 1000);
            })
            ->first();

            // Check if tariff bases were found
            if (!$tariffBases) {
                $tariffValue = $trajet['TARIF_BASE_ABO_SCOL'] / 1000;
                echo "No tariff base found for subs_type 1 with tariff: {$tariffValue} (original: {$trajet['TARIF_BASE_ABO_SCOL']})\n";
                continue;
            }

            // Check if trip already exists
            $existingTrip = Trip::where('id_station_start', $startStation->id)
                ->where('id_station_end', $endStation->id)
                ->whereHas('tariffOptions', function ($query) {
                    $query->whereNot( function ($q) {
                        $q->where('id_subs_type', 3)
                            ->where('is_regular', true);
                    });
                })
                ->first();

            if ($existingTrip) {
                // add line to existing trip sync
                $existingTrip->lines()->syncWithoutDetaching([$line->id]);
                // Trip exists, update it
                $existingTariffOption = $existingTrip->tariffOptions()
                    ->where('id_subs_type', 3)
                    ->where('is_regular', false)
                    ->where('id_tariff_base' , $tariffBases->id)
                    ->first();

                if (!$existingTariffOption) {
                    $existingTrip->tariffOptions()->create([
                        'id_subs_type' => 3,
                        'is_regular' => false,
                        'id_tariff_base' => $tariffBases->id,
                    ]);
                }

                // Update number_of_km if existing is null or 0 and new data has value
                if (($existingTrip->number_of_km === null || $existingTrip->number_of_km == 0) &&
                    $trajet['KILOMERAGE'] && $trajet['KILOMERAGE'] > 0) {
                    $existingTrip->update(['number_of_km' => $trajet['KILOMERAGE']]);
                }
            } else {
                // Trip does not exist, create a new one
                $trip = Trip::create([
                    'nom_fr' => $startStation->nom_fr . ' - ' . $endStation->nom_fr,
                    'nom_en' => $startStation->nom_en . ' - ' . $endStation->nom_en,
                    'nom_ar' => $startStation->nom_ar . ' - ' . $endStation->nom_ar,
                    'id_station_start' => $startStation->id,
                    'id_station_end' => $endStation->id,
                    'CODE_ABO_SCOL' => null,
                    'CODE_ABO_CIVIL' => null,
                    'status' => true,
                    'inter_station' => false,
                    'is_aller_retour' => true,
                    'number_of_km' => $trajet['KILOMERAGE'],
                ]);

                // Attach the line to the trip using many-to-many relationship
                $trip->lines()->attach($line->id);

                $trip->tariffOptions()->create([
                    'id_subs_type' => 3,
                    'is_regular' => false,
                    'id_tariff_base' => $tariffBases->id
                ])->save();

                echo "Created new trip: {$trip->nom_fr}\n";
            }
        }

        // Save missed stations to file
        $this->saveMissedStations();
    }

    private function addMissedStation($stationName)
    {
        if (!in_array($stationName, $this->missedStations)) {
            $this->missedStations[] = $stationName;
        }
    }

    private function saveMissedStations()
    {
        if (!empty($this->missedStations)) {
            $filePath = database_path('seeders/missed_stations.txt');

            // Read existing content if file exists
            $existingStations = [];
            if (file_exists($filePath)) {
                $existingContent = file_get_contents($filePath);
                $existingStations = array_filter(explode("\n", $existingContent));
            }

            // Merge with new missed stations (avoiding duplicates)
            $allStations = array_unique(array_merge($existingStations, $this->missedStations));
            sort($allStations);

            // Write back to file
            file_put_contents($filePath, implode("\n", $allStations));

            echo "Saved " . count($this->missedStations) . " missed stations from TripIrregulierSeeder to missed_stations.txt\n";
        }
    }
}


