<?php


use App\Http\Controllers\AffectationCardTypesController;
use App\Http\Controllers\SubsCardController;
use App\Models\AffectationCardType;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CaptchaController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\RolePermissionsController;
use App\Http\Controllers\MotifDuplicateController;
use App\Http\Controllers\GovernorateController;
use App\Http\Controllers\DelegationController;
use App\Http\Controllers\AgencyController;
use App\Http\Controllers\EstablishmentController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\GovernoratePurchaseOrderController;
use App\Http\Controllers\TypeEstablishmentController;
use App\Http\Controllers\DegreeController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\CardTypeController;
use App\Http\Controllers\SubsTypeController;
use App\Http\Controllers\TariffBaseController;
use App\Http\Controllers\TariffController;
use App\Http\Controllers\IncreaseController;
use App\Http\Controllers\LineController;
use App\Http\Controllers\StationController;
use App\Http\Controllers\SeasonController;
use App\Http\Controllers\TripController;
use App\Http\Controllers\TariffOptionController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\SalePeriodController;
use App\Http\Controllers\SalePointController;
use App\Http\Controllers\AffectationAgentController;
use App\Http\Controllers\TypeClientController;
use App\Http\Controllers\PeriodicityController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\CardFeeController;
use App\Http\Controllers\OptionController;
use App\Http\Controllers\ConfigController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\website\WebsiteLinesController;
use App\Http\Controllers\website\WebsiteTripController;
use App\Http\Controllers\website\WebsiteBusLocationController;
use App\Http\Controllers\LocationTypeController;
use App\Http\Controllers\LocationSeasonController;
use App\Http\Controllers\TypeVehiculeController;
use App\Http\Controllers\TypeVehicleTypeLocationController;
use App\Http\Controllers\TypeVehiculeSaisonLocationController;
use App\Http\Controllers\SocialAffairController;
use App\Http\Controllers\AcademicYearController;
use App\Http\Controllers\SubsDuplicationController;
use App\Http\Controllers\StockCardController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\CardSequenceController;
use App\Http\Controllers\AuditController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application.
|
*/


Route::get('/captcha',  [CaptchaController::class, 'generateCaptcha']);
Route::post('/forget-password',  [AuthController::class, 'forgetPassword']);
Route::post('/verif-code-password',  [AuthController::class, 'verifcoderesetPassword']);
Route::post('/reset-password',  [AuthController::class, 'resetPassword']);


Route::post('/login-admin', [AuthController::class, 'loginAdmin'])
    ->middleware('throttle:15,1');

Route::group(
    [
        'where' => ['id' => '[0-9]+'],
        'middleware' => ['jwt.auth'],
    ],
    function () {
        Route::get('/verify-token', [AuthController::class, 'verifyToken']);
        Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
        Route::get('/logout-admin', [AuthController::class, 'logoutAdmin']);

        Route::get('/admins-all', [AdminController::class, 'all']);
        Route::apiResource('admins', AdminController::class);
        Route::post('admins/{admin}/roles', [AdminController::class, 'assignRoles'])->name('admins.assign-roles');

        Route::get('/roles-all', [RolePermissionsController::class, 'all']);
        Route::get('/roles', [RolePermissionsController::class, 'index']);
        Route::get('/role/{id}/permissions', [RolePermissionsController::class, 'getAssignedUnassignedPermissionsForRole']);
        Route::get('/admin/{id}/roles', [RolePermissionsController::class, 'getAssignedUnassignedRolesForAdmin']);
        Route::post('/roles/{role}/assign-permissions', [RolePermissionsController::class, 'assignPermissions']);
        Route::post('/roles/{role}/remove-permissions', [RolePermissionsController::class, 'removePermissions']);
        Route::post('/users/{adminId}/assign-roles', [RolePermissionsController::class, 'assignRolesToUser']);
        Route::post('/users/{adminId}/remove-roles', [RolePermissionsController::class, 'removeRolesFromUser']);
        Route::post('/role-store', [RolePermissionsController::class, 'store']);
        Route::put('/role-update/{id}', [RolePermissionsController::class, 'update']);
        Route::delete('/role-delete/{id}', [RolePermissionsController::class, 'destroy']);

        Route::get('/governorates-all', [GovernorateController::class, 'all']);
        Route::apiResource('governorates', GovernorateController::class);

        Route::get('/delegations-all', [DelegationController::class, 'all']);
        Route::get('/delegations/governorate/{governorate}', [DelegationController::class, 'getByGovernorate']);
        Route::apiResource('delegations', DelegationController::class);

        Route::get('/agencies-all', [AgencyController::class, 'all']);
        Route::get('/agencies/delegation/{delegation}', [AgencyController::class, 'getByDelegation']);
        Route::get('/agencies/governorate/{governorate}', [AgencyController::class, 'getByGovernorate']);
        Route::apiResource('agencies', AgencyController::class);

        Route::get('/clients/delegation/{delegation}', [ClientController::class, 'getByDelegation']);
        Route::get('/clients/governorate/{governorate}', [ClientController::class, 'getByGovernorate']);
        Route::get('/clients/type/{type}', [ClientController::class, 'getByClientType']);
        Route::post('/clients/search-student', [ClientController::class, 'searchStudent']);
        Route::get('/clients-has-cin', [ClientController::class, 'getClientsHasCIN']);
        Route::get('/clients-impersonal', [ClientController::class, 'getImpersonalClients']);
        Route::get('/clients-conventional', [ClientController::class, 'getConventionalClients']);
        Route::apiResource('clients', ClientController::class);


        Route::get('/governorate-purchase-orders/{governorate}', [GovernoratePurchaseOrderController::class, 'getByGovernorate']);
        Route::apiResource('governorate-purchase-orders', GovernoratePurchaseOrderController::class);

        Route::get('/motif-duplicates-all', [MotifDuplicateController::class, 'all']);
        Route::apiResource('motif-duplicates', MotifDuplicateController::class);

        Route::get('/establishments-all', [EstablishmentController::class, 'all']);
        Route::get('/establishments/delegation/{delegation}', [EstablishmentController::class, 'getByDelegation']);
        Route::get('/establishments/type/{type}', [EstablishmentController::class, 'getByType']);
        Route::apiResource('establishments', EstablishmentController::class);

        Route::get('/type-establishments-all', [TypeEstablishmentController::class, 'all']);
        Route::apiResource('type-establishments', TypeEstablishmentController::class);

        Route::get('/degrees-all', [DegreeController::class, 'all']);
        Route::get('/degrees/type/{type}', [DegreeController::class, 'getByType']);
        Route::apiResource('degrees', DegreeController::class);

        Route::get('/payment-methods-all', [PaymentMethodController::class, 'all']);
        Route::apiResource('payment-methods', PaymentMethodController::class);

        Route::get('/card-types-all', [CardTypeController::class, 'all']);
        Route::apiResource('card-types', CardTypeController::class);

        Route::get('/subs-types-all', [SubsTypeController::class, 'all']);
        Route::apiResource('subs-types', SubsTypeController::class);

        Route::get('/tariff-bases-all', [TariffBaseController::class, 'all']);
        Route::get('/tariff-bases/subs-type/{subsTypeId}', [TariffBaseController::class, 'getBySubsType']);
        Route::apiResource('tariff-bases', TariffBaseController::class);

        Route::get('/tariffs-all', [TariffController::class, 'all']);
        Route::get('/tariffs/tariff-base/{tariffBaseId}', [TariffController::class, 'getByTariffBase']);
        Route::apiResource('tariffs', TariffController::class);

        Route::get('/lines-all', [LineController::class, 'all']);
        Route::post('/lines/assign-stations', [LineController::class, 'assignStations']);
        Route::get('/lines/by-trip/{tripId}', [LineController::class, 'getLinesByTrip']);
        Route::get('/lines/by-stations/{departureStationId}/{arrivalStationId}/{subsTypeId}', [LineController::class, 'getLinesByStations']);
        Route::get('/lines/config/stations/{departureStationId}/{arrivalStationId}', [LineController::class, 'getLinesConfigByStations']);
        Route::get('/lines/{line}/stations-routes', [LineController::class, 'getStationsAndRoutes']);
        Route::put('lines/{line}/stations-routes', [LineController::class, 'updateStationsAndRoutes'])
            ->name('lines.update-stations-routes');
        Route::apiResource('lines', LineController::class);

        Route::get('/increases-all', [IncreaseController::class, 'all']);
        Route::apiResource('increases', IncreaseController::class);

        Route::get('/stations-all', [StationController::class, 'all']);
        Route::get('/stations/subscription-type/{subsTypeId}', [StationController::class, 'getStationsBySubscriptionType']);
        Route::get('/stations/{stationId}/connected-stations/{subsTypeId}', [StationController::class, 'getConnectedStationsForTrip']);
        Route::get('/stations/{station}/arrival-stations', [StationController::class, 'getArrivalStations']);
        Route::apiResource('stations', StationController::class);

        Route::get('/seasons-all', [SeasonController::class, 'all']);
        Route::apiResource('seasons', SeasonController::class);

        Route::get('/trips-all', [TripController::class, 'all']);
        Route::get('/trips-not-inter-all', [TripController::class, 'allNotInter']);
        Route::post('/trips/check-exists', [TripController::class, 'checkTripExists']);
        Route::apiResource('trips', TripController::class);

        Route::apiResource('tariff-options', TariffOptionController::class);

        Route::get('/campaigns/{campaign}/sales-periods', [SalePeriodController::class, 'getByCampaign']);
        Route::get('/campaigns-all', [CampaignController::class, 'all']);
        Route::apiResource('campaigns', CampaignController::class);

        Route::get('/sales-periods-all', [SalePeriodController::class, 'all']);
        Route::apiResource('sales-periods', SalePeriodController::class);

        Route::get('/agencies/{agency}/sales-points', [SalePointController::class, 'getByAgency']);
        Route::get('/sale-points-all', [SalePointController::class, 'all']);
        Route::apiResource('sale-points', SalePointController::class);

        Route::get('agent-affectations-all', [AffectationAgentController::class, 'all']);
        Route::get('/agent-affectations/by-point/{salePointId}/{salePeriodId}', [AffectationAgentController::class, 'getAgentsBySalePoint']);
        Route::apiResource('agent-affectations', AffectationAgentController::class);

        Route::get('agent-cards-affectations-all', [AffectationCardTypesController::class, 'all']);
        Route::get('/agent-cards-affectations/by-point/{salePointId}/{salePeriodId}', [AffectationCardTypesController::class, 'getByPoint']);
        Route::apiResource('agent-cards-affectations', AffectationCardTypesController::class);

        Route::get('/type-clients-all', [TypeClientController::class, 'all']);
        Route::apiResource('type-clients', TypeClientController::class);

        Route::get('/periodicities-all', [PeriodicityController::class, 'all']);
        Route::apiResource('periodicities', PeriodicityController::class);

        Route::get('/discounts-all', [DiscountController::class, 'all']);
        Route::apiResource('discounts', DiscountController::class);

        Route::get('/card-fees-all', [CardFeeController::class, 'all']);
        Route::apiResource('card-fees', CardFeeController::class);

        Route::get('/options-all', [OptionController::class, 'all']);
        Route::apiResource('options', OptionController::class);

        // Subscription Routes
        Route::get('/subscriptions-all', [SubscriptionController::class, 'all']);
        Route::get('/subscriptions/client/{clientId}', [SubscriptionController::class, 'getByClient']);
        Route::get('/subscriptions/subs-type/{subsTypeId}', [SubscriptionController::class, 'getBySubsType']);
        Route::get('/subscriptions/trip/{tripId}', [SubscriptionController::class, 'getByTrip']);
        Route::get('/subscriptions/periodicity/{periodicityId}', [SubscriptionController::class, 'getByPeriodicity']);
        Route::get('/subscriptions/parent/{parentId}', [SubscriptionController::class, 'getByParent']);
        Route::apiResource('subscriptions', SubscriptionController::class);

        Route::post('/transactions/process-payment', [TransactionController::class, 'processPayment']);
        Route::post('/transactions/{subscriptionId}/cancel', [TransactionController::class, 'cancelTransaction']);

        // Config Routes
        Route::get('/configs-all', [ConfigController::class, 'all']);
        Route::get('/configs/group/{group}', [ConfigController::class, 'getByGroup']);
        Route::get('/configs/public', [ConfigController::class, 'getPublic']);
        Route::get('/configs/system', [ConfigController::class, 'getSystem']);
        Route::get('/configs/key/{key}', [ConfigController::class, 'getByKey']);
        Route::post('/configs/key/{key}', [ConfigController::class, 'setByKey']);
        Route::apiResource('configs', ConfigController::class);

        // Location Types Routes
        Route::get('/location-types-all', [LocationTypeController::class, 'all']);
        Route::apiResource('location-types', LocationTypeController::class);

        // Location Seasons Routes
        Route::get('/location-seasons-all', [LocationSeasonController::class, 'all']);
        Route::get('/location-seasons-active', [LocationSeasonController::class, 'active']);
        Route::apiResource('location-seasons', LocationSeasonController::class);

        // Type Vehicule Routes
        Route::get('/type-vehicules-all', [TypeVehiculeController::class, 'all']);
        Route::apiResource('type-vehicules', TypeVehiculeController::class);

        // Type Vehicle Type Location Routes
        Route::get('/type-vehicle-type-locations-all', [TypeVehicleTypeLocationController::class, 'all']);
        Route::get('/type-vehicle-type-locations/type-vehicule/{typeVehiculeId}', [TypeVehicleTypeLocationController::class, 'getByTypeVehicule']);
        Route::get('/type-vehicle-type-locations/location-type/{locationTypeId}', [TypeVehicleTypeLocationController::class, 'getByLocationType']);
        Route::apiResource('type-vehicle-type-locations', TypeVehicleTypeLocationController::class);

        // Type Vehicule Saison Location Routes
        Route::get('/type-vehicule-saison-locations-all', [TypeVehiculeSaisonLocationController::class, 'all']);
        Route::get('/type-vehicule-saison-locations/type-vehicule/{typeVehiculeId}', [TypeVehiculeSaisonLocationController::class, 'getByTypeVehicule']);
        Route::get('/type-vehicule-saison-locations/season/{seasonId}', [TypeVehiculeSaisonLocationController::class, 'getBySeason']);
        Route::apiResource('type-vehicule-saison-locations', TypeVehiculeSaisonLocationController::class);

        // Social Affairs Routes
        Route::get('/social-affairs-all', [SocialAffairController::class, 'all']);
        Route::get('/social-affairs/governorate/{governorate}', [SocialAffairController::class, 'getByGovernorate']);
        Route::post('/social-affairs/import', [SocialAffairController::class, 'import']);
        Route::post('/social-affairs/verify', [SocialAffairController::class, 'verifyCinParent']);
        Route::apiResource('social-affairs', SocialAffairController::class);

        // Academic Years Routes
        Route::get('/academic-years-all', [AcademicYearController::class, 'all']);
        Route::apiResource('academic-years', AcademicYearController::class);


        // SubsDuplication
        Route::apiResource('subsduplications', SubsDuplicationController::class);

        Route::get('/stock-cards-all', [StockCardController::class, 'all']);
        Route::get('/stock-cards/latest/{id_card_type}', [StockCardController::class, 'getLatestRecord']);
        Route::get('/stock-cards/between/{id_card_type}/{debSequence}', [StockCardController::class, 'getIntervalleSequence']);
        Route::apiResource('stock-cards', StockCardController::class);

        // Card Sequences
        Route::get('/card-sequences', [CardSequenceController::class, 'index']);
        Route::get('/card-sequences/summary', [CardSequenceController::class, 'getSummary']);
        Route::get('/card-sequences/available/{cardTypeId}', [CardSequenceController::class, 'getAvailableSequences']);
        Route::get('/card-sequences/occupied/{cardTypeId}', [CardSequenceController::class, 'getOccupiedSequences']);

        Route::get('/subs-cards-all', [SubsCardController::class, 'all']);
        Route::get('/subs-cards/by-subscription/{subsId}', [SubsCardController::class, 'getSubsCardsBySubscription']);
        Route::apiResource('subs-cards', SubsCardController::class);

        // Statistics Routes
        Route::prefix('statistics')->group(function () {
            Route::get('/dashboard', [StatisticsController::class, 'dashboard']);
            Route::get('/subscribers-by-trip', [StatisticsController::class, 'subscribersByTrip']);
            Route::get('/subscribers-by-establishment', [StatisticsController::class, 'subscribersByEstablishment']);
            Route::get('/subscribers-by-line', [StatisticsController::class, 'subscribersByLine']);
            Route::get('/subscribers-by-kilometric-range', [StatisticsController::class, 'subscribersByKilometricRange']);
            Route::get('/subscribers-by-discount', [StatisticsController::class, 'subscribersByDiscount']);
            Route::get('/revenues-by-sale-period', [StatisticsController::class, 'revenuesBySalePeriod']);
            Route::get('/revenues-by-client-governorate', [StatisticsController::class, 'revenuesByClientGovernorate']);
            Route::get('/revenues-by-payment-method', [StatisticsController::class, 'revenuesByPaymentMethod']);
            Route::get('/subscribers-by-agency', [StatisticsController::class, 'subscribersByAgency']);
            Route::get('/subscribers-by-sale-point', [StatisticsController::class, 'subscribersBySalePoint']);
            Route::get('/subscriptions-by-subs-type', [StatisticsController::class, 'subscriptionsBySubsType']);
        });

        // Audit Routes
        Route::prefix('audits')->group(function () {
            Route::get('/', [AuditController::class, 'index']);
            Route::get('/statistics', [AuditController::class, 'getStatistics']);
            Route::get('/recent-activity', [AuditController::class, 'getRecentActivity']);
            Route::get('/most-active-users', [AuditController::class, 'getMostActiveUsers']);
            Route::get('/most-audited-models', [AuditController::class, 'getMostAuditedModels']);
            Route::get('/model-types', [AuditController::class, 'getModelTypes']);
            Route::get('/event-types', [AuditController::class, 'getEventTypes']);
            Route::get('/my-audits', [AuditController::class, 'getMyAudits']);
            Route::get('/model/{modelType}/{modelId}', [AuditController::class, 'getModelAudits']);
            Route::get('/user/{userId}', [AuditController::class, 'getUserAudits']);
            Route::get('/event/{event}', [AuditController::class, 'getEventAudits']);
            Route::get('/{id}', [AuditController::class, 'show']);
        });
    }
);


/*
|--------------------------------------------------------------------------
| API Routes for Public Website
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the public website.
|
*/
Route::prefix('website')->group(function () {
    /*---------------------------
    |  horaires + street map
    ----------------------------*/
    // get all lines
    Route::get('/lines', [WebsiteLinesController::class, 'getLines']);

    // get line details
    Route::get('/lines/{line}', [WebsiteLinesController::class, 'getLineDetails']);

    /*---------------------------
    |  Itiniraires
    ----------------------------*/
    // get all stations
    Route::get('/stations-all', [WebsiteTripController::class, 'stations_all']);

    // get all seasons
    Route::get('/seasons-all', [WebsiteTripController::class, 'seasons_all']);

    // get station by selected station
    Route::get('/stations/{station}', [WebsiteTripController::class, 'relatedStations']);

    // get website trips with start and end station
    Route::get("/trips/stations/{startStation}/{endStation}/season/{season}", [WebsiteTripController::class, 'getTripsByStations']);

    /*---------------------------
    |  Bus Locations
    ----------------------------*/
    Route::post('/bus-locations', [WebsiteBusLocationController::class, 'getLocationAmount']);
    Route::get('/location-types', [LocationTypeController::class, 'all']);
    Route::get('/type-vehicules', [TypeVehiculeController::class, 'all']);
});


/*
|--------------------------------------------------------------------------
| External API Routes (Protected by API Key)
|--------------------------------------------------------------------------
|
| These routes are designed for external applications and are protected
| by API key authentication. Used for integrations and batch processing.
|
*/
Route::prefix('external')->middleware(['api.key', 'throttle:60,1'])->group(function () {
    Route::get('/transactions/previous-day', [TransactionController::class, 'getPreviousDayTransactions']);
});



