<?php

namespace Database\Seeders;

use App\Models\Increase;
use App\Models\Line;
use Illuminate\Database\Seeder;


class LineSeeder extends Seeder
{
    public function run(): void
    {
        $ligne = array(
            array('CODE_LIGNE' => '101','LIBELLE' => 'NABEUL-KAIROUAN','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '50'),
            array('CODE_LIGNE' => '102','LIBELLE' => 'NABEUL-TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '67'),
            array('CODE_LIGNE' => '105','LIBELLE' => 'HAMMAMET-TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '67'),
            array('CODE_LIGNE' => '110','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '111','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '112','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '115','LIBELLE' => 'NABEUL-HAMMAMET-YASMINE HAMMAMET','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '120','LIBELLE' => 'NABEUL-HAMMAMET-YASMINE HAMMAMET','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '125','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '140','LIBELLE' => 'MAAMOURA-NABEUL-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '140 (1)','LIBELLE' => 'SOMAA-NABEUL-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '141','LIBELLE' => 'NABEUL-ZAGHOUAN-FAHS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '145','LIBELLE' => 'HAMMAMET-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '41'),
            array('CODE_LIGNE' => '160','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '165','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '190','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '191','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '214','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '215','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '216','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '217','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '218','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '220','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '225','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '226','LIBELLE' => 'SOLIMAN-MENZEL BOUZELFA-GROMBALIA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '227','LIBELLE' => 'SOLIMAN-MENZEL BOUZELFA-GROMBALIA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '34'),
            array('CODE_LIGNE' => '246','LIBELLE' => 'HAMMAMET-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '41'),
            array('CODE_LIGNE' => '295','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '302','LIBELLE' => 'BENI KHALLED- TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '50'),
            array('CODE_LIGNE' => '310','LIBELLE' => 'MENZEL BOUZALFA-BENI KHALLED-GROMBALIA','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '40'),
            array('CODE_LIGNE' => '320','LIBELLE' => 'MENZEL BOUZELFA-BATROU-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '40'),
            array('CODE_LIGNE' => '340','LIBELLE' => 'BENI KHALLED-TUNIS-KELIBIA','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '341','LIBELLE' => 'ZAWIT JEDIDI-BENI KHALLED-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '360','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '390','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '395','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '420','LIBELLE' => 'KORBA-TAZARKA-SOMAA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '37'),
            array('CODE_LIGNE' => '440','LIBELLE' => 'TAZARKA-KORBA-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '441','LIBELLE' => 'MIDA-MENZEL BOUZELFA-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '520','LIBELLE' => 'KELIBIA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '40'),
            array('CODE_LIGNE' => '535','LIBELLE' => 'KELIBIA-KORBA-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '536','LIBELLE' => 'MENZEL TMIM-TUNIS(via ENCHAA)','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '36'),
            array('CODE_LIGNE' => '590','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '591','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '601','LIBELLE' => 'KELIBIA-NABEUL-MONASTIR','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '42'),
            array('CODE_LIGNE' => '602','LIBELLE' => 'KELIBIA-TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '604','LIBELLE' => 'KELIBIA-NABEUL-KAIROUAN','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '50'),
            array('CODE_LIGNE' => '620','LIBELLE' => 'KELIBIA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '40'),
            array('CODE_LIGNE' => '640','LIBELLE' => 'KELIBIA-KORBA-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '641','LIBELLE' => 'HAMMEM GHEZAZ-MENZEL BOUZELFA-TUNIS(via OM DHWIL)','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '690','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '701','LIBELLE' => 'HAOUARIA-NABEUL-MAHDIA','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '39'),
            array('CODE_LIGNE' => '702','LIBELLE' => 'HAOUARIA-TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '720','LIBELLE' => 'HAOUARIA-KELIBIA-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '39'),
            array('CODE_LIGNE' => '721','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '740','LIBELLE' => 'HAOUARIA-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '790','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '802','LIBELLE' => 'ZAGHOUAN-TUNIS','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '840','LIBELLE' => 'ZRIBA VILLAGE-ZAGOUAN-TUNIS','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '841','LIBELLE' => 'ZAGHOUAN-BOUACHIR-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '841 (1)','LIBELLE' => 'ZAGHOUAN-JRADOU-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '842','LIBELLE' => 'ZAGHOUAN-ENNFIDHA-SOUSSE','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '890','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '891','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '893','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '895','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '896','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '940','LIBELLE' => 'FAHS-NABEUL','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '50'),
            array('CODE_LIGNE' => '941','LIBELLE' => 'FAHS-ENNADHOUR-SOUSSE','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '942','LIBELLE' => 'FAHS-ENNADHOUR-ESSBIKHA-KAIROUAN','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '48'),
            array('CODE_LIGNE' => '990','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '991','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '622','LIBELLE' => 'KELIBIA-NABEUL-CITE UNIVERSITAIRE (Direct)','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45')
        );

        $ligne_arab = array(
            array('CODE_LIGNE' => '101','LIBELLE' => 'نابل-القيروان','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '102','LIBELLE' => 'نابل-تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '67'),
            array('CODE_LIGNE' => '105','LIBELLE' => 'الحمامات-تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '67'),
            array('CODE_LIGNE' => '110','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '111','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '112','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '115','LIBELLE' => 'نابل-الحمامات-ياسمين الحمامات','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '120','LIBELLE' => 'نابل-الحمامات-ياسمين الحمامات','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '125','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '140','LIBELLE' => 'المعمورة-نابل-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '140 (1)','LIBELLE' => 'الصمعة-نابل-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '141','LIBELLE' => 'نابل-زغوان-الفحص','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '145','LIBELLE' => 'الحمامات-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '160','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '165','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '190','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '191','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '214','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '215','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '216','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '217','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '218','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '220','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '225','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '226','LIBELLE' => 'سليمان-منزل بوزلفة-قرمبالية','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '227','LIBELLE' => 'سليمان-منزل بوزلفة-قرمبالية','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '246','LIBELLE' => 'الحمامات-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '295','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '302','LIBELLE' => 'بني خلاد- تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '310','LIBELLE' => 'منزل بوزلفة-بني خلاد-قرمبال','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '320','LIBELLE' => 'منزل بوزلفة-باطرو-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '340','LIBELLE' => 'بني خلاد-تونس-قليبية','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '341','LIBELLE' => 'زاوية الجديدي-بني خلاد-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '360','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '390','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '395','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '420','LIBELLE' => 'قربة-تازركة-الصمعة-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '440','LIBELLE' => 'تازركة-قربة-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '441','LIBELLE' => 'الميدة-منزل بوزلفة-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '520','LIBELLE' => 'قليبية-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '535','LIBELLE' => 'قليبية-قربة-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '536','LIBELLE' => 'منزل تميم-تونس عبر النشع','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '590','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '591','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '601','LIBELLE' => 'قليبية-نابل-المنستير','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '602','LIBELLE' => 'قليبية-تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '604','LIBELLE' => 'قليبية-نابل-القيروان','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '620','LIBELLE' => 'قليبية-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '640','LIBELLE' => 'قليبية-قربة-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '641','LIBELLE' => 'حمام الغزاز-منزل بوزلفة-تون','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '690','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '701','LIBELLE' => 'الهوارية-نابل-المهدية','TYPE_LIGNE' => '2','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '702','LIBELLE' => 'الهوارية-تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '720','LIBELLE' => 'الهوارية-قليبية-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '721','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '740','LIBELLE' => 'الهوارية-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '790','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '802','LIBELLE' => 'زغوان-تونس','TYPE_LIGNE' => '1','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '55'),
            array('CODE_LIGNE' => '840','LIBELLE' => 'الزريبة قرية-زغوان-تونس','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '841','LIBELLE' => 'زغوان-بوعشير-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '45'),
            array('CODE_LIGNE' => '841 (1)','LIBELLE' => 'زغوان-جرادو-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '842','LIBELLE' => 'زغوان-النفيضة-سوسة','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '890','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '891','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '893','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '895','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '896','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '940','LIBELLE' => 'الفحص-نابل','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '941','LIBELLE' => 'الفحص-الناظور-سوسة','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '942','LIBELLE' => 'الفحص-الناظور-السبيخة-القيروان','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '35'),
            array('CODE_LIGNE' => '990','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0'),
            array('CODE_LIGNE' => '991','LIBELLE' => '','TYPE_LIGNE' => '3','TYPE_SERVICE' => '0','ETAT' => '0','VITESSE_COMMERCIALE' => '0')
        );

        // First, insert French/English lines
        foreach ($ligne as $line) {
            Line::create([
                'nom_fr' => $line['LIBELLE'],
                'nom_en' => $line['LIBELLE'],
                'nom_ar' => '', // Will be updated in the next loop
                'CODE_LINE' => $line['CODE_LIGNE'],
                'type_service' => $line['TYPE_SERVICE'] === '0' ? 'normal' : 'confort',
                'status' => $line['ETAT'] === '0',
                'commercial_speed' => $line['VITESSE_COMMERCIALE']
            ]);
        }

        // Then, update Arabic names for lines that have translations
        foreach ($ligne_arab as $line) {
            Line::where('CODE_LINE', $line['CODE_LIGNE'])
                ->update(['nom_ar' => $line['LIBELLE']]);
        }

        // add some randome increaser for 10% of lines
        $lines = Line::all();
        $increases = Increase::all();
        $count = $lines->count();
        $randomLines = $lines->random((int) ($count * 0.1));
        foreach ($randomLines as $line) {
            $increase = $increases->random();
            $line->update([
                'increase_id' => $increase->id,
                'status' => true // Set the line to active
            ]);
        }
    }
}




