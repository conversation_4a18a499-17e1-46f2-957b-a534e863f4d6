<?php

namespace App\Repositories;

use App\Models\Increase;
use Prettus\Repository\Eloquent\BaseRepository;

class IncreaseRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'nom_fr' => 'like',
        'nom_en' => 'like',
        'nom_ar' => 'like',
        'percentage' => 'between',
        'date_subscription' => 'between',
        'date_website' => 'between'
    ];

    public function model(): string
    {
        return Increase::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
    }
}
